import {
  AddProductAnswerSuccessResponse,
  approveRejectCoachInterface,
  BodyBuildingProgramListResponse,
  BodyBuildingProgramResponse,
  BodyBuildingProgramSuccessRes,
  ChangePasswordRequest,
  ChangeStatusModel,
  Club,
  CreateBlogPostRequestBody,
  CreateBlogPostResponse,
  CreateBodyBuildingProgramReq,
  CreateBrandRequest,
  CreateBrandSuccessResponse,
  CreateCategoryRequest,
  CreateCategorySuccessResponse,
  CreateClubRequestBody,
  CreateCoachRequestBody,
  CreateCoachSuccessResponse,
  CreateIntermediateTrainingRequest,
  CreateIntermediateTrainingResponse,
  CreateManufacturerRequest,
  CreateManufacturerSuccessResponse,
  CreateMuscleGroupBody,
  CreateMuscleGroupResponse,
  CreateProductSuccessResponse,
  CreateSubscriptionPackageResponse,
  CreateTagRequestBody,
  CreateTagSuccessResponse,
  CreateTrainingCategoryBody,
  CreateTrainingCategoryResponse,
  CreateTrainingRequest,
  CreateTrainingResponse,
  DeleteBlogSuccessResponse,
  DeleteClubSuccessResponse,
  DeleteCoachCategorySuccessRes,
  DeleteIntermediateTrainingResponse,
  DeleteManufacturerSuccessResponse,
  DeleteMuscleGroupResponse,
  DeleteProductSuccessResponse,
  DeleteReviewListResponse,
  DeleteTrainingCategoryResponse,
  DeleteTrainingResponse,
  FileUploadRequestBody,
  FindUsersListSuccessResponse,
  GetAllBlogPostsSuccessResponse,
  GetAllBrandsSuccessResponse,
  GetAllClubsSuccessResponse,
  GetAllEventSuccessResponse,
  GetAllProductsSuccessResponse,
  GetAllSubscriberListSuccessResponse,
  GetAllSubscriptionPackageResponse,
  GetBlogSuccessResponse,
  GetCategoryListSuccessResponse,
  GetCategoryRequest,
  GetCategorySuccessResponse,
  GetCoachCategoriesSuccessResponse,
  GetDashBoardCountSuccessResponse,
  GetExerciseListSuccessResponse,
  GetIntermediateTrainingListResponse,
  GetIntermediateTrainingResponse,
  GetManufacturersSuccessResponse,
  GetManufacturerSuccessResponse,
  GetMultipleCoachSuccessResponse,
  GetMuscleGroupListResponse,
  GetMuscleGroupResponse,
  GetProductParams,
  GetProductSuccessResponse,
  GetSingleCoachCategorySuccessResponse,
  GetSingleCoachSuccessResponse,
  GetTagsSuccessResponse,
  GetTagSuccessResponse,
  GetTrainingCategoryListResponse,
  GetTrainingCategoryResponse,
  GetTrainingResponse,
  IAmountPerPointSuccessRes,
  ICreateBaseChallengeReq,
  ICreateBaseChallengeSuccessRes,
  ICreateBaseEventReq,
  ICreateBaseExcerciseReq,
  ICreateBaseExcerciseSuccessRes,
  ICreateBasePollReq,
  ICreateBasePollSuccessRes,
  ICreateEventSuccessRes,
  IDeleteBaseChallengeSuccessRes,
  IDeletePollSuccessRes,
  IGetAllPollsSuccessRes,
  IGetBaseChallengeByIdSuccessRes,
  IGetBaseChallengeSuccessRes,
  IGetBaseExcerciseSuccessRes,
  IPointsPaymentRes,
  ISelectPollWinnerReq,
  IUpdateBaseChallengeReq,
  IUpdateBaseEventReq,
  IUpdateBaseExcerciseReq,
  IUpdateEventSuccessRes,
  IUserChallengeHistorySuccessRes,
  OrderStatusModelResponse,
  Product,
  ProductQuestionsForAdminSuccessResponse,
  ProductQuestionsWithAnswerForAdminSuccessResponse,
  PublicUploadFileSuccessResponse,
  ReviewListResponse,
  SignInRequest,
  SignInSuccessResponse,
  SubscriberSuccessResponse,
  SuccessResponse,
  UpdateBlogPostRequestBody,
  UpdateBlogPostResponse,
  UpdateBodyBuildingProgramBody,
  UpdateBrandRequest,
  UpdateBrandSuccessResponse,
  updateCoachCategoryRequestBody,
  UpdateCoachCategorySuccessResponse,
  UpdatedAdminRequest,
  UpdateIntermediateTrainingBody,
  UpdateIntermediateTrainingResponse,
  UpdateManufacturerRequest,
  UpdateManufacturerSuccessResponse,
  UpdateMuscleGroupBody,
  UpdateMuscleGroupResponse,
  UpdateProductSuccessResponse,
  UpdateTrainingBody,
  UpdateTrainingCategoryBody,
  UpdateTrainingCategoryResponse,
  UpdateTrainingResponse,
  UploadFileSuccessResponse,
} from 'models';
import {
  UpdatePollRequestBody,
  UpdatePollResponse,
} from 'models/poll/updatePoll.interface';
import { NextRouter } from 'next/router';

export interface User {
  id?: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  phone?: string;
  username: string;
  email: string;
  password?: string;
  provider?: string;
  providerData?: object;
  additionalProviderData?: object;
  resetPasswordToken?: string;
  resetPasswordExpires?: number;
  gender?: string;
  addresses?: Address[];
  status: string;
}

export interface Address {
  id?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  country: string;
  postCode: string;
}

export interface apiFunction {
  getOrderEnum: () => Promise<OrderStatusModelResponse | any>;
  getAllOrderList: (
    orderStatus: string,
    paymentStatus: string,
    shippingStatus: string,
    startDate: string,
    endDate: string,
    skip: number,
    limit: number
  ) => Promise<any | undefined>;
  getSingleOrderById: (id: string) => Promise<any | undefined>;
  updateOrderStatus: (data: ChangeStatusModel) => Promise<any | undefined>;
  updatePaymentStatus: (data: ChangeStatusModel) => Promise<any | undefined>;
  updateShippingStatus: (data: any) => Promise<any | undefined>;
  getAllManufacturers(): Promise<GetManufacturersSuccessResponse | any>;
  updateProduct: (
    data: any,
    id: any,
    router: any
  ) => Promise<UpdateProductSuccessResponse | any>;
  createProduct: (
    data: any,
    router: any
  ) => Promise<CreateProductSuccessResponse | any>;
  getProduct: (
    data: GetProductParams
  ) => Promise<GetProductSuccessResponse | any>;
  getProducts: (
    skip: number,
    limit: number
  ) => Promise<GetAllProductsSuccessResponse | any>;
  searchProduct: (data: string) => Promise<Product | undefined>;
  getCategoryList: () => Promise<GetCategoryListSuccessResponse | any>;
  createCategory: (
    data: CreateCategoryRequest,
    router: NextRouter
  ) => Promise<CreateCategorySuccessResponse | any>;
  getCategory: (
    id: GetCategoryRequest
  ) => Promise<GetCategorySuccessResponse | any>;
  deleteProduct: (
    productId: string
  ) => Promise<DeleteProductSuccessResponse | any>;
  signin: (data: SignInRequest) => Promise<SignInSuccessResponse | any>;
  createAdmin: (data: User, cb: any) => Promise<User | undefined>;
  getAdmins: () => Promise<User[] | undefined>;
  updateAdmin: (
    data: UpdatedAdminRequest,
    router: NextRouter
  ) => Promise<SuccessResponse | undefined>;
  changePassword: (
    data: ChangePasswordRequest,
    router: NextRouter
  ) => Promise<SuccessResponse | undefined>;
  createManufacturer: (
    data: CreateManufacturerRequest,
    router: NextRouter
  ) => Promise<CreateManufacturerSuccessResponse | any>;
  getManufacturer: (
    skip: number,
    limit: number
  ) => Promise<GetManufacturersSuccessResponse | any>;
  deleteManufacturer: (
    id: any,
    router: NextRouter
  ) => Promise<DeleteManufacturerSuccessResponse | any>;
  getSingleManufacturer: (
    data: any
  ) => Promise<GetManufacturerSuccessResponse | any>;
  updateManufacturer: (
    data: UpdateManufacturerRequest,
    id: string,
    router: any
  ) => Promise<UpdateManufacturerSuccessResponse | any>;
  getUserProfile: (router: NextRouter) => Promise<SuccessResponse | undefined>;
  getBrands: (
    skip: number,
    limit: number
  ) => Promise<GetAllBrandsSuccessResponse | any>;
  getBrand(brandId: any): Promise<any>;
  getTags: () => Promise<GetTagsSuccessResponse | any>;
  createBrand(
    data: CreateBrandRequest,
    router: NextRouter
  ): Promise<CreateBrandSuccessResponse | any>;
  deleteBrand(productId: string): Promise<boolean | any>;
  updateBrand(
    brandID: string,
    data: UpdateBrandRequest,
    router: NextRouter
  ): Promise<UpdateBrandSuccessResponse | any>;
  getAllTags(): Promise<GetTagsSuccessResponse | any>;
  createTags(
    data: CreateTagRequestBody,
    router: NextRouter
  ): Promise<CreateTagSuccessResponse | any>;
  getSingleTag(id: String): Promise<GetTagSuccessResponse | any>;
  createClub(data: CreateClubRequestBody): Promise<SuccessResponse>;
  getClubs(
    skip: number,
    limit: number
  ): Promise<GetAllClubsSuccessResponse | any>;
  deleteClub(clubId: string): Promise<DeleteClubSuccessResponse | any>;
  updateClub(data: Club): Promise<SuccessResponse>;
  getClubMembersByStatus(
    clubId: string,
    status: string
  ): Promise<SuccessResponse>;
  updateClubMember(requsetId: string, date?: string): Promise<SuccessResponse>;
  deleteClubMember(clubId: string, id: string): Promise<SuccessResponse>;
  blockClubMember(clubId: string, id: string): Promise<SuccessResponse>;
  addClubMember(
    clubId: string,
    userId: string,
    date: string
  ): Promise<SuccessResponse>;
  uploadMedia(data: FileUploadRequestBody): Promise<UploadFileSuccessResponse>;
  createBaseExercise(
    data: ICreateBaseExcerciseReq
  ): Promise<ICreateBaseExcerciseSuccessRes>;
  getBaseExercise(
    type?: string,
    name?: string,
    skip?: number,
    limit?: number
  ): Promise<IGetBaseExcerciseSuccessRes | any>;
  updateBaseExercise(
    id: string,
    data: IUpdateBaseExcerciseReq
  ): Promise<ICreateBaseExcerciseSuccessRes>;
  deleteBaseExercise(id: string): Promise<ICreateBaseExcerciseSuccessRes>;
  getChallenges(
    skip?: number,
    limit?: number
  ): Promise<IGetBaseChallengeSuccessRes | any>;
  createChallenge(
    data: ICreateBaseChallengeReq
  ): Promise<ICreateBaseChallengeSuccessRes>;
  deleteChallenge(id: string): Promise<IDeleteBaseChallengeSuccessRes | any>;
  getAcceptedChallenge(
    status?: string,
    skip?: number,
    limit?: number
  ): Promise<IUserChallengeHistorySuccessRes | any>;
  editAcceptedChallenge(newStatus: string, id: string): Promise<string | any>;
  updateChallenge(
    id: string,
    data: IUpdateBaseChallengeReq
  ): Promise<ICreateBaseChallengeSuccessRes>;
  createTrainingCategory(
    data: CreateTrainingCategoryBody
  ): Promise<CreateTrainingCategoryResponse>;
  getTrainingCategory(
    id?: string,
    skip?: number,
    limit?: number
  ): Promise<
    GetTrainingCategoryListResponse | GetTrainingCategoryResponse | any
  >;
  updateTrainingCategory(
    id: string,
    data: UpdateTrainingCategoryBody
  ): Promise<UpdateTrainingCategoryResponse>;
  deleteTrainingCategory(
    id: string
  ): Promise<DeleteTrainingCategoryResponse | any>;
  createMuscleGroup(
    data: CreateMuscleGroupBody
  ): Promise<CreateMuscleGroupResponse>;
  getMuscleGroup(
    id?: string,
    skip?: number,
    limit?: number
  ): Promise<GetMuscleGroupListResponse | GetMuscleGroupResponse | any>;
  updateMuscleGroup(
    id: string,
    data: UpdateMuscleGroupBody
  ): Promise<UpdateMuscleGroupResponse>;
  deleteMuscleGroup(id: string): Promise<DeleteMuscleGroupResponse>;
  createTraining(data: CreateTrainingRequest): Promise<CreateTrainingResponse>;
  getTrainingList(
    // filterBy?: string,
    // name?: string,
    level?: string,
    skip?: number,
    limit?: number
  ): Promise<GetExerciseListSuccessResponse | any>;
  getTrainingById(id: string): Promise<GetTrainingResponse>;
  updateTraining(
    id: string,
    data: UpdateTrainingBody
  ): Promise<UpdateTrainingResponse>;
  deleteTraining(id: string): Promise<DeleteTrainingResponse>;
  getAdvertisingProducts(skip: number, limit: number): Promise<any>;
  createAdvertisingProduct(id: string): Promise<any>;
  deleteAdvertisingProduct(id: string): Promise<any>;
  getDealProducts(skip: number, limit: number): Promise<any>;
  createDealProduct(id: string): Promise<any>;
  deleteDealProduct(id: string): Promise<any>;
  getUnansweredQuestionList(
    skip: number,
    limit: number
  ): Promise<ProductQuestionsForAdminSuccessResponse | any>;
  answerQuestion(
    productId: string,
    questionId: string,
    answer: string
  ): Promise<AddProductAnswerSuccessResponse | any>;
  getAnsweredQuestionList(
    skip: number,
    limit: number
  ): Promise<ProductQuestionsWithAnswerForAdminSuccessResponse | any>;
  createBBProgram(
    data: CreateBodyBuildingProgramReq
  ): Promise<BodyBuildingProgramSuccessRes>;
  getBBProgram(
    id?: string,
    skip?: number,
    limit?: number
  ): Promise<
    BodyBuildingProgramListResponse | BodyBuildingProgramResponse | any
  >;
  updateBBProgram(
    id: string,
    data: UpdateBodyBuildingProgramBody
  ): Promise<BodyBuildingProgramResponse>;
  deleteBBProgram(id: string): Promise<BodyBuildingProgramResponse>;
  createIntermediateTraining(
    data: CreateIntermediateTrainingRequest
  ): Promise<CreateIntermediateTrainingResponse>;
  getIntermediateTrainingList(
    skip: number,
    limit: number
  ): Promise<GetIntermediateTrainingListResponse | any>;
  getSingleIntermediateTraining(
    id: string
  ): Promise<GetIntermediateTrainingResponse>;
  deleteIntermediateTraining(
    id: string
  ): Promise<DeleteIntermediateTrainingResponse>;
  updateIntermediateTraining(
    id: string,
    data: UpdateIntermediateTrainingBody
  ): Promise<UpdateIntermediateTrainingResponse>;
  uploadPublicMedia(
    file: any,
    featureName: string
  ): Promise<PublicUploadFileSuccessResponse>;
  getChallengeById(id: string): Promise<IGetBaseChallengeByIdSuccessRes>;
  getBaseExerciseById(id: string): Promise<ICreateBaseExcerciseSuccessRes>;
  getClubsFromGoogleMap(queries: string): Promise<any>;
  getClubDetailsFromGoogleMap(placeId: string): Promise<any>;
  setPoint(amount: number): Promise<IPointsPaymentRes | any>;
  getPoint(): Promise<IAmountPerPointSuccessRes | any>;
  setPaymentMethod(list: any): Promise<any>;
  getPaymentMethod(): Promise<any>;
  getProductReviews(
    productId: string,
    skip: number,
    limit: number
  ): Promise<ReviewListResponse | any>;
  deleteProductReview(
    reviewId: string,
    userId: string
  ): Promise<DeleteReviewListResponse | any>;
  createShipment(orderId: any, parcel?: any): Promise<any>;
  buyShipment(orderId: any): Promise<any>;
  getShipmentDetails(orderId: string): Promise<any>;
  getWaitList(
    skip: number,
    limit: number
  ): Promise<SubscriberSuccessResponse | any>;
  createBlog(
    data: CreateBlogPostRequestBody
  ): Promise<CreateBlogPostResponse | any>;
  getBlogs(
    skip: number,
    limit: number
  ): Promise<GetAllBlogPostsSuccessResponse | any>;
  deleteBlogs(id: string): Promise<DeleteBlogSuccessResponse | any>;
  getBlogById(id: string): Promise<GetBlogSuccessResponse | any>;
  updateBlog(
    id: string,
    data: UpdateBlogPostRequestBody
  ): Promise<UpdateBlogPostResponse | any>;
  createPackage(data: any): Promise<CreateSubscriptionPackageResponse | any>;
  getPackages(
    skip: number,
    limit: number
  ): Promise<GetAllSubscriptionPackageResponse | any>;
  getPackageById(id: string): Promise<CreateSubscriptionPackageResponse | any>;
  updatePackage(
    id: string,
    data: any
  ): Promise<CreateSubscriptionPackageResponse | any>;
  getDashboardData(
    startDate: string,
    endDate: string,
    endpoint: string
  ): Promise<GetDashBoardCountSuccessResponse | any>;
  getUserList(
    skip: number,
    limit: number
  ): Promise<FindUsersListSuccessResponse | any>;
  getSubscriberList(
    skip: number,
    limit: number
  ): Promise<GetAllSubscriberListSuccessResponse | any>;
  deleteUser(userName: string, email: string): Promise<any>;
  getWpBlogs(page: number, limit: number, token: string): Promise<any>;
  getWpSingleBlog(slug: string, token: string): Promise<any>;
  createPoll(data: ICreateBasePollReq): Promise<ICreateBasePollSuccessRes>;
  getPolls(skip: number, limit: number): Promise<IGetAllPollsSuccessRes | any>;
  updatePoll(
    id: string,
    data: UpdatePollRequestBody
  ): Promise<UpdatePollResponse | any>;
  deletePoll(id: string): Promise<IDeletePollSuccessRes | any>;
  selectPollWinner(
    data: ISelectPollWinnerReq
  ): Promise<ICreateBasePollSuccessRes>;
  createEvent(data: ICreateBaseEventReq): Promise<ICreateEventSuccessRes>;
  getMultipleEvents(
    skip: number,
    limit: number
  ): Promise<GetAllEventSuccessResponse | any>;
  getSingleEvent(id: string): Promise<any>;
  deleteEvent(id: string): Promise<any>;
  updateEvent(
    id: string,
    data: IUpdateBaseEventReq
  ): Promise<IUpdateEventSuccessRes>;
  getMultipleCoachProfiles(
    skip: number,
    limit: number,
    adminReviewStatus?: string
  ): Promise<GetMultipleCoachSuccessResponse | any>;
  getSingleCoachProfile(id: string): Promise<GetSingleCoachSuccessResponse>;
  approvePendingCoachProfile(
    id: string,
    data: approveRejectCoachInterface
  ): Promise<GetSingleCoachSuccessResponse>;
  rejectPendingCoachProfile(
    id: string,
    data: approveRejectCoachInterface
  ): Promise<GetSingleCoachSuccessResponse>;
  createCoachCategory(
    data: CreateCoachRequestBody
  ): Promise<CreateCoachSuccessResponse | any>;
  getCoachCategories(): Promise<GetCoachCategoriesSuccessResponse | any>;
  getSingleCoachCategory(
    id: string
  ): Promise<GetSingleCoachCategorySuccessResponse | any>;
  updateCoachCategory(
    id: string,
    data: updateCoachCategoryRequestBody
  ): Promise<UpdateCoachCategorySuccessResponse | any>;
  deleteCoachCategory(id: string): Promise<DeleteCoachCategorySuccessRes | any>;
  // Tasks - Admin Only
  getTasks(skip: number, limit: number, filters?: any): Promise<any>;
  getTaskById(id: string): Promise<any>;
  createTask(data: any): Promise<any>;
  updateTask(id: string, data: any): Promise<any>;
  deleteTask(id: string): Promise<any>;
  // Referral Campaigns - Admin Only
  getReferralCampaigns(offset?: number, limit?: number, adminCampaignStatus?: string): Promise<any>;
  getSingleReferralCampaign(id: string): Promise<any>;
  createReferralCampaign(data: any): Promise<any>;
  updateReferralCampaign(id: string, data: any): Promise<any>;
  // Accounting - Admin Only
  getAccountingAccounts(skip?: number, limit?: number): Promise<any>;
  getAccountingAccount(accountId: string): Promise<any>;
}
export interface adminCreate {
  id?: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  phone?: string;
  username?: string;
  email: string;
  provider?: string;
  providerData?: object;
  additionalProviderData?: object;
  resetPasswordToken?: string;
  resetPasswordExpires?: number;
  gender?: string;
  addresses?: Address[];
  status?: string;
  password?: string;
  newPassword?: string;
}
