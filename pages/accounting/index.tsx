import type { NextPage } from 'next';
import { useEffect, useState, useCallback } from 'react';
import { userAPI } from '@/APIs';
import { AccountingTransaction } from 'models/accounting';
import { toast } from 'react-toastify';
import PrevNextPagination from '@/components/common/newPagination';
import { AccountingTable, AccountingFilters as AccountingFiltersComponent } from '@/components/accounting';

interface AccountingFiltersType {
  transactionType?: 'credit' | 'debit' | '';
  referenceType?: string;
}

const AccountingPage: NextPage = () => {
  const [transactions, setTransactions] = useState<AccountingTransaction[]>([]);
  const [allTransactions, setAllTransactions] = useState<AccountingTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [skip, setSkip] = useState(0);
  const [limit] = useState(10);
  const [filters, setFilters] = useState<AccountingFiltersType>({});

  const fetchTransactions = useCallback(async () => {
    setLoading(true);
    try {
      const response = await userAPI.getAccountingAccounts(0, 1000); // Fetch all transactions
      
      if ('data' in response) {
        setAllTransactions(response.data);
      } else {
        toast.error(response.error?.message || 'Failed to fetch accounting transactions');
      }
    } catch (error) {
      toast.error('Failed to fetch accounting transactions');
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter and paginate transactions locally
  const filteredTransactions = allTransactions.filter(transaction => {
    if (filters.transactionType && transaction.transactionType !== filters.transactionType) {
      return false;
    }
    if (filters.referenceType && !transaction.referenceType.toLowerCase().includes(filters.referenceType.toLowerCase())) {
      return false;
    }
    return true;
  });

  const paginatedTransactions = filteredTransactions.slice(skip, skip + limit);

  const handleFilterChange = (key: keyof AccountingFiltersType, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === '' ? undefined : value,
    }));
    setSkip(0); // Reset to first page when filters change
  };

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Accounting Transactions</div>
        </div>

        <AccountingFiltersComponent
          filters={filters}
          onFilterChange={handleFilterChange}
        />

        <AccountingTable
          transactions={paginatedTransactions}
          loading={loading}
        />

        {filteredTransactions.length > limit && (
          <PrevNextPagination
            skip={skip}
            setSkip={setSkip}
            limit={limit}
            disableNext={skip + limit >= filteredTransactions.length}
          />
        )}
      </main>
    </>
  );
};

export default AccountingPage;
