export interface AccountingTransaction {
  id: string;
  transactionType: 'credit' | 'debit';
  reference: string;
  referenceId: string;
  referenceType: string;
  amount: number;
}

export interface AccountingAccount {
  data: AccountingTransaction[];
}

export interface GetAccountingAccountsSuccessResponse {
  data: AccountingTransaction[];
}

export interface GetAccountingAccountSuccessResponse {
  data: AccountingTransaction[];
}