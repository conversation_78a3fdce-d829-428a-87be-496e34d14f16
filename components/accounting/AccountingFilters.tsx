import React from 'react';

interface AccountingFilters {
  transactionType?: 'credit' | 'debit' | '';
  referenceType?: string;
}

interface AccountingFiltersProps {
  filters: AccountingFilters;
  onFilterChange: (key: keyof AccountingFilters, value: any) => void;
}

const AccountingFiltersComponent: React.FC<AccountingFiltersProps> = ({
  filters,
  onFilterChange,
}) => {
  return (
    <div className="card">
      <div className="card-body">
        <div className="row g-3">
          <div className="col-md-4">
            <label className="form-label">Transaction Type</label>
            <select
              className="form-select"
              value={filters.transactionType || ''}
              onChange={(e) => onFilterChange('transactionType', e.target.value)}
            >
              <option value="">All Types</option>
              <option value="credit">Credit</option>
              <option value="debit">Debit</option>
            </select>
          </div>
          
          <div className="col-md-4">
            <label className="form-label">Reference Type</label>
            <input
              type="text"
              className="form-control"
              placeholder="Filter by reference type..."
              value={filters.referenceType || ''}
              onChange={(e) => onFilterChange('referenceType', e.target.value)}
            />
          </div>

          <div className="col-md-4 d-flex align-items-end">
            <button
              type="button"
              className="btn btn-outline-secondary"
              onClick={() => {
                onFilterChange('transactionType', '');
                onFilterChange('referenceType', '');
              }}
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountingFiltersComponent;
