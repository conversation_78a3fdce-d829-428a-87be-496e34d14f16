import React from 'react';
import { AccountingTransaction } from 'models/accounting';

interface AccountingTableProps {
  transactions: AccountingTransaction[];
  loading: boolean;
}

const AccountingTable: React.FC<AccountingTableProps> = ({
  transactions,
  loading,
}) => {
  const getTransactionTypeBadge = (type: 'credit' | 'debit') => {
    const badgeClass = type === 'credit' ? 'bg-success' : 'bg-danger';
    return (
      <span className={`badge ${badgeClass}`}>
        {type.toUpperCase()}
      </span>
    );
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center mt-5">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="card border-1 mt-3 rounded px-2">
      <div className="card-body">
        <div className="table-responsive">
          <table className="table table-hover">
            <thead>
              <tr>
                <th scope="col">ID</th>
                <th scope="col">Type</th>
                <th scope="col">Reference</th>
                <th scope="col">Reference Type</th>
                <th scope="col">Amount</th>
              </tr>
            </thead>
            <tbody>
              {transactions.length === 0 ? (
                <tr>
                  <td colSpan={5} className="text-center py-4">
                    No accounting transactions found
                  </td>
                </tr>
              ) : (
                transactions.map((transaction) => (
                  <tr key={transaction.id}>
                    <td>
                      <small className="text-muted">{transaction.id}</small>
                    </td>
                    <td>{getTransactionTypeBadge(transaction.transactionType)}</td>
                    <td>
                      <div>
                        <strong>{transaction.reference}</strong>
                        <br />
                        <small className="text-muted">ID: {transaction.referenceId}</small>
                      </div>
                    </td>
                    <td>
                      <span className="badge bg-secondary">
                        {transaction.referenceType}
                      </span>
                    </td>
                    <td>
                      <strong className={transaction.transactionType === 'credit' ? 'text-success' : 'text-danger'}>
                        {transaction.transactionType === 'credit' ? '+' : '-'}{formatAmount(transaction.amount)}
                      </strong>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AccountingTable;
